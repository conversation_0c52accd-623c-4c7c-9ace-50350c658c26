import type {
	GetCCAppointmentType,
	GetCCCustomField,
	GetCCLocationType,
	GetCCPatientType,
	GetCCResourceType,
	GetCCServiceType,
	GetCCUserType,
	GetInvoiceType,
	GetPaymentType,
	PostCCAppointmentType,
	PostCCPatientType,
} from "@type";
import { getConfig, getLoggingConfig } from "@utils/configs";
import { createApiClient, extractResponseData } from "./request";

/**
 * Helper function for conditional logging based on configuration
 * @param message - Log message
 * @param data - Optional data to log when context is enabled
 */
function logInfo(message: string, data?: unknown): void {
	const showContext = getLoggingConfig("showLogContext");
	if (showContext && data) {
		console.log(message, data);
	} else {
		console.log(message);
	}
}

/**
 * Helper function for debug logging based on configuration
 * @param message - Debug message
 * @param data - Optional data to log
 */
function logDebug(message: string, data?: unknown): void {
	const showDebugLogs = getLoggingConfig("showDebugLogs");
	if (showDebugLogs) {
		const showContext = getLoggingConfig("showLogContext");
		if (showContext && data) {
			console.log(`[DEBUG] ${message}`, data);
		} else {
			console.log(`[DEBUG] ${message}`);
		}
	}
}

/**
 * Helper function for standardized API performance logging
 * @param method - HTTP method
 * @param status - HTTP status code
 * @param duration - Duration in milliseconds
 * @param path - Relative API path
 */
function logApiPerformance(method: string, status: number, duration: number, path: string): void {
	const durationSeconds = (duration / 1000).toFixed(1);
	const ccApiDomain = getConfig("ccApiDomain") as string;
	const fullUrl = `${ccApiDomain}${path}`;
	console.log(`[${method}] [${status}] -> ${durationSeconds}s -> ${fullUrl}`);
}

/**
 * CC (CliniCore) API client for medical practice management platform
 *
 * This class provides a comprehensive interface to the CliniCore API, handling all
 * communication with the medical practice management platform. It implements proper
 * authentication, error handling, and response parsing for all CC API operations.
 *
 * **Supported Operations:**
 * - Patient management (create, read, update, search)
 * - Appointment management (create, read, update, cancel, delete)
 * - Invoice and payment operations
 * - Custom field management
 * - Location and resource management
 * - User management
 *
 * **Authentication:**
 * Uses Bearer token authentication with API key from configuration.
 * The API key is automatically included in all requests.
 *
 * **Error Handling:**
 * - Implements retry logic for transient failures
 * - Provides detailed error messages for debugging
 * - Handles rate limiting and API quotas
 * - Logs all API errors for monitoring
 *
 * **Performance:**
 * - Optimized for Cloudflare Workers environment
 * - Efficient request/response handling
 * - Proper async/await patterns
 * - Stateless serverless architecture
 *
 * @example
 * ```typescript
 * // Get a patient by ID
 * const patient = await ccClient.patient.get(12345);
 *
 * // Create a new patient
 * const newPatient = await ccClient.patient.create({
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phoneMobile: "+**********"
 * });
 *
 * // Search for patients
 * const patients = await ccClient.patient.search("<EMAIL>");
 *
 * // Create an appointment
 * const appointment = await ccClient.appointment.create({
 *   patientId: 12345,
 *   startTime: "2024-01-01T10:00:00Z",
 *   endTime: "2024-01-01T11:00:00Z",
 *   title: "Consultation"
 * });
 * ```
 *
 * @see {@link createApiClient} for underlying HTTP client implementation
 * @see {@link extractResponseData} for response parsing utilities
 *
 * @since 1.0.0
 * @version 1.0.0
 */
class CCApiClient {
	/**
	 * HTTP client instance configured for CC API
	 * @private
	 */
	private client: ReturnType<typeof createApiClient>;

	/**
	 * Creates a new CC API client instance
	 *
	 * Initializes the HTTP client with proper authentication headers and base URL
	 * from the application configuration. The client is ready to use immediately
	 * after construction.
	 *
	 * @throws {Error} If CC API domain or key is not configured
	 *
	 * @example
	 * ```typescript
	 * // Client is automatically instantiated as singleton
	 * import { ccClient } from '@api/ccClient';
	 *
	 * // Use the client directly
	 * const patient = await ccClient.patient.get(123);
	 * ```
	 */
	constructor() {
		const ccApiDomain = getConfig("ccApiDomain") as string;
		const ccApiKey = getConfig("ccApiKey");

		logDebug(`🔧 Initializing CC API Client - Domain: ${ccApiDomain}, Key: ${ccApiKey ? 'SET' : 'NOT SET'}`);

		this.client = createApiClient(ccApiDomain, {
			Authorization: `Bearer ${ccApiKey}`,
		});

		logDebug(`✅ CC API Client initialized successfully`);
	}

	/**
	 * Patient operations
	 */
	patient = {
		/**
		 * Create a new patient
		 * @param data - Patient data
		 * @returns Created patient
		 */
		create: async (data: PostCCPatientType): Promise<GetCCPatientType> => {
			const startTime = Date.now();
			logDebug(`🏥 CC API: Creating patient - ${data.firstName} ${data.lastName} (${data.email})`);

			const response = await this.client<{ patient: GetCCPatientType }>({
				url: "/patients",
				method: "POST",
				data: { patient: data },
			});

			const duration = Date.now() - startTime;
			logApiPerformance("POST", response.status, duration, "/patients");

			const result = extractResponseData(response, "patient") as GetCCPatientType;
			logDebug(`✅ CC API: Patient created successfully - ID: ${result.id}`);
			return result;
		},

		/**
		 * Update a patient
		 * @param id - Patient ID
		 * @param data - Patient data to update
		 * @returns Updated patient
		 */
		update: async (
			id: number,
			data: PostCCPatientType,
		): Promise<GetCCPatientType> => {
			const startTime = Date.now();
			logDebug(`📝 CC API: Updating patient - ID: ${id}, Name: ${data.firstName} ${data.lastName}`);

			const response = await this.client<{ patient: GetCCPatientType }>({
				url: `/patients/${id}`,
				method: "PUT",
				data: { patient: data },
			});

			const duration = Date.now() - startTime;
			logApiPerformance("PUT", response.status, duration, `/patients/${id}`);

			const result = extractResponseData(response, "patient") as GetCCPatientType;
			logDebug(`✅ CC API: Patient updated successfully - ID: ${result.id}`);
			return result;
		},

		/**
		 * Get a patient by ID
		 * @param id - Patient ID
		 * @returns Patient data
		 */
		get: async (id: number): Promise<GetCCPatientType> => {
			const startTime = Date.now();
			logDebug(`🔍 CC API: Getting patient - ID: ${id}`);

			const response = await this.client<{ patient: GetCCPatientType }>({
				url: `/patients/${id}`,
				method: "GET",
			});

			const duration = Date.now() - startTime;
			logApiPerformance("GET", response.status, duration, `/patients/${id}`);

			const result = extractResponseData(response, "patient") as GetCCPatientType;
			logDebug(`✅ CC API: Patient retrieved successfully - ${result.firstName} ${result.lastName}`);
			return result;
		},

		/**
		 * Search patients
		 * @param params - Search parameters
		 * @returns Array of patients
		 */
		search: async (params: {
			email?: string;
			phone?: string;
			firstName?: string;
			lastName?: string;
			limit?: number;
			offset?: number;
		}): Promise<GetCCPatientType[]> => {
			const queryParams: Record<string, string> = {};

			if (params.email) queryParams.email = params.email;
			if (params.phone) queryParams.phone = params.phone;
			if (params.firstName) queryParams.firstName = params.firstName;
			if (params.lastName) queryParams.lastName = params.lastName;
			if (params.limit) queryParams.limit = params.limit.toString();
			if (params.offset) queryParams.offset = params.offset.toString();

			const response = await this.client<{ patients: GetCCPatientType[] }>({
				url: "/patients",
				method: "GET",
				params: queryParams,
			});
			return extractResponseData(response, "patients");
		},

		/**
		 * Get all patients
		 * @param params - Query parameters
		 * @returns Array of patients
		 */
		all: async (params?: {
			limit?: number;
			offset?: number;
			search?: string;
		}): Promise<GetCCPatientType[]> => {
			const queryParams: Record<string, string> = {};

			if (params?.limit) queryParams.limit = params.limit.toString();
			if (params?.offset) queryParams.offset = params.offset.toString();
			if (params?.search) queryParams.search = params.search;

			const response = await this.client<{ patients: GetCCPatientType[] }>({
				url: "/patients",
				method: "GET",
				params: Object.keys(queryParams).length > 0 ? queryParams : undefined,
			});
			return extractResponseData(response, "patients");
		},

		/**
		 * Delete a patient
		 * @param id - Patient ID
		 */
		delete: async (id: number): Promise<void> => {
			await this.client({
				url: `/patients/${id}`,
				method: "DELETE",
			});
		},
	};

	/**
	 * Appointment operations
	 */
	appointment = {
		/**
		 * Create a new appointment
		 * @param data - Appointment data
		 * @returns Created appointment
		 */
		create: async (
			data: PostCCAppointmentType,
		): Promise<GetCCAppointmentType> => {
			const response = await this.client<{ appointment: GetCCAppointmentType }>(
				{
					url: "/appointments",
					method: "POST",
					data: { appointment: data },
				},
			);
			return extractResponseData(response, "appointment");
		},

		/**
		 * Update an appointment
		 * @param id - Appointment ID
		 * @param data - Appointment data to update
		 * @returns Updated appointment
		 */
		update: async (
			id: number,
			data: Partial<PostCCAppointmentType>,
		): Promise<GetCCAppointmentType> => {
			const response = await this.client<{ appointment: GetCCAppointmentType }>(
				{
					url: `/appointments/${id}`,
					method: "PUT",
					data: { appointment: data },
				},
			);
			return extractResponseData(response, "appointment");
		},

		/**
		 * Get an appointment by ID
		 * @param id - Appointment ID
		 * @returns Appointment data
		 */
		get: async (id: number): Promise<GetCCAppointmentType> => {
			const response = await this.client<{ appointment: GetCCAppointmentType }>(
				{
					url: `/appointments/${id}`,
					method: "GET",
				},
			);
			return extractResponseData(response, "appointment");
		},

		/**
		 * Cancel an appointment
		 * @param id - Appointment ID
		 * @param reason - Cancellation reason
		 * @returns Updated appointment
		 */
		cancel: async (
			id: number,
			reason?: string,
		): Promise<GetCCAppointmentType> => {
			const response = await this.client<{ appointment: GetCCAppointmentType }>(
				{
					url: `/appointments/${id}`,
					method: "PUT",
					data: {
						appointment: {
							canceledWhy: reason || "Cancelled via API",
						},
					},
				},
			);
			return extractResponseData(response, "appointment");
		},

		/**
		 * Delete an appointment
		 * @param id - Appointment ID
		 */
		delete: async (id: number): Promise<void> => {
			await this.client({
				url: `/appointments/${id}`,
				method: "DELETE",
			});
		},

		/**
		 * Get appointments for a patient
		 * @param patientId - Patient ID
		 * @returns Array of appointments
		 */
		getByPatient: async (
			patientId: number,
		): Promise<GetCCAppointmentType[]> => {
			const response = await this.client<{
				appointments: GetCCAppointmentType[];
			}>({
				url: "/appointments",
				method: "GET",
				params: { patient: patientId.toString() },
			});
			return extractResponseData(response, "appointments");
		},
	};

	/**
	 * Invoice operations
	 */
	invoice = {
		/**
		 * Get an invoice by ID
		 * @param id - Invoice ID
		 * @returns Invoice data
		 */
		get: async (id: number): Promise<GetInvoiceType> => {
			const response = await this.client<{ invoice: GetInvoiceType }>({
				url: `/invoices/${id}`,
				method: "GET",
			});
			return extractResponseData(response, "invoice");
		},

		/**
		 * Get invoices by IDs
		 * @param ids - Array of invoice IDs
		 * @returns Array of invoices
		 */
		getMultiple: async (ids: number[]): Promise<GetInvoiceType[]> => {
			const promises = ids.map((id) => this.invoice.get(id));
			return Promise.all(promises);
		},

		/**
		 * Get invoices for a patient
		 * @param patientId - Patient ID
		 * @returns Array of invoices
		 */
		getByPatient: async (patientId: number): Promise<GetInvoiceType[]> => {
			const response = await this.client<{ invoices: GetInvoiceType[] }>({
				url: "/invoices",
				method: "GET",
				params: { patient: patientId.toString() },
			});
			return extractResponseData(response, "invoices");
		},
	};

	/**
	 * Payment operations
	 */
	payment = {
		/**
		 * Get a payment by ID
		 * @param id - Payment ID
		 * @returns Payment data
		 */
		get: async (id: number): Promise<GetPaymentType> => {
			const response = await this.client<{ payment: GetPaymentType }>({
				url: `/payments/${id}`,
				method: "GET",
			});
			return extractResponseData(response, "payment");
		},

		/**
		 * Get payments by IDs
		 * @param ids - Array of payment IDs
		 * @returns Array of payments
		 */
		getMultiple: async (ids: number[]): Promise<GetPaymentType[]> => {
			const promises = ids.map((id) => this.payment.get(id));
			return Promise.all(promises);
		},

		/**
		 * Get payments for a patient
		 * @param patientId - Patient ID
		 * @returns Array of payments
		 */
		getByPatient: async (patientId: number): Promise<GetPaymentType[]> => {
			const response = await this.client<{ payments: GetPaymentType[] }>({
				url: "/payments",
				method: "GET",
				params: { patient: patientId.toString() },
			});
			return extractResponseData(response, "payments");
		},
	};

	/**
	 * Custom field operations
	 */
	customField = {
		/**
		 * Get all custom fields
		 * @returns Array of custom fields
		 */
		all: async (): Promise<GetCCCustomField[]> => {
			const response = await this.client<{ customFields: GetCCCustomField[] }>({
				url: "/custom-fields",
				method: "GET",
			});
			return extractResponseData(response, "customFields");
		},

		/**
		 * Get a custom field by ID
		 * @param id - Custom field ID
		 * @returns Custom field data
		 */
		get: async (id: number): Promise<GetCCCustomField> => {
			const response = await this.client<{ customField: GetCCCustomField }>({
				url: `/custom-fields/${id}`,
				method: "GET",
			});
			return extractResponseData(response, "customField");
		},
	};

	/**
	 * Location operations
	 */
	location = {
		/**
		 * Get all locations
		 * @returns Array of locations
		 */
		all: async (): Promise<GetCCLocationType[]> => {
			const response = await this.client<{ locations: GetCCLocationType[] }>({
				url: "/locations",
				method: "GET",
			});
			return extractResponseData(response, "locations");
		},

		/**
		 * Get a location by ID
		 * @param id - Location ID
		 * @returns Location data
		 */
		get: async (id: number): Promise<GetCCLocationType> => {
			const response = await this.client<{ location: GetCCLocationType }>({
				url: `/locations/${id}`,
				method: "GET",
			});
			return extractResponseData(response, "location");
		},
	};

	/**
	 * Resource operations
	 */
	resource = {
		/**
		 * Get all resources
		 * @returns Array of resources
		 */
		all: async (): Promise<GetCCResourceType[]> => {
			const response = await this.client<{ resources: GetCCResourceType[] }>({
				url: "/resources",
				method: "GET",
			});
			return extractResponseData(response, "resources");
		},

		/**
		 * Get a resource by ID
		 * @param id - Resource ID
		 * @returns Resource data
		 */
		get: async (id: number): Promise<GetCCResourceType> => {
			const response = await this.client<{ resource: GetCCResourceType }>({
				url: `/resources/${id}`,
				method: "GET",
			});
			return extractResponseData(response, "resource");
		},
	};

	/**
	 * Service operations
	 */
	service = {
		/**
		 * Get all services
		 * @returns Array of services
		 */
		all: async (): Promise<GetCCServiceType[]> => {
			const response = await this.client<{ services: GetCCServiceType[] }>({
				url: "/services",
				method: "GET",
			});
			return extractResponseData(response, "services");
		},

		/**
		 * Get a service by ID
		 * @param id - Service ID
		 * @returns Service data
		 */
		get: async (id: number): Promise<GetCCServiceType> => {
			const response = await this.client<{ service: GetCCServiceType }>({
				url: `/services/${id}`,
				method: "GET",
			});
			return extractResponseData(response, "service");
		},
	};

	/**
	 * User operations
	 */
	user = {
		/**
		 * Get all users
		 * @returns Array of users
		 */
		all: async (): Promise<GetCCUserType[]> => {
			const response = await this.client<{ users: GetCCUserType[] }>({
				url: "/users",
				method: "GET",
			});
			return extractResponseData(response, "users");
		},

		/**
		 * Get a user by ID
		 * @param id - User ID
		 * @returns User data
		 */
		get: async (id: number): Promise<GetCCUserType> => {
			const response = await this.client<{ user: GetCCUserType }>({
				url: `/users/${id}`,
				method: "GET",
			});
			return extractResponseData(response, "user");
		},
	};
}

// Export singleton instance
export const ccClient = new CCApiClient();
