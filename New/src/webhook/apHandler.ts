import { getDb } from "@database";
import { appointment, patient } from "@database/schema";
import type {
	GetAPAppointmentType,
	GetAPContactType,
	WebhookCalendar,
	WebhookContext,
} from "@type";
import { getLoggingConfig } from "@utils/configs";
import { logError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";
import type { Context } from "hono";
import {
	processAPAppointmentCreate,
	processAPAppointmentDelete,
	processAPAppointmentUpdate,
} from "../processors/apAppointmentProcessor";
import {
	processAPContactCreate,
	processAPContactDelete,
	processAPContactUpdate,
} from "../processors/apContactProcessor";

/**
 * Helper function for debug logging based on configuration
 * @param message - Debug message
 * @param data - Optional data to log
 */
function logDebug(message: string, data?: unknown): void {
	const showDebugLogs = getLoggingConfig("showDebugLogs");
	if (showDebugLogs) {
		const showContext = getLoggingConfig("showLogContext");
		if (showContext && data) {
			console.log(`[DEBUG] ${message}`, data);
		} else {
			console.log(`[DEBUG] ${message}`);
		}
	}
}

/**
 * AP Webhook Event structure based on v3Integration patterns
 */
interface APWebhookEvent {
	/** Event type from AP */
	type:
		| "appointment_created"
		| "appointment_updated"
		| "appointment_deleted"
		| "contact_created"
		| "contact_updated"
		| "contact_deleted";
	/** Event data */
	data: {
		/** Calendar data for appointments */
		calendar?: GetAPAppointmentType & {
			appointmentId: string;
			startTime: string;
			endTime: string;
			created_by_meta?: { source?: string };
			last_updated_by_meta?: { source?: string };
		};
		/** Contact data */
		contact?: GetAPContactType;
		/** Contact ID for appointments */
		contact_id?: string;
		/** Additional fields */
		email?: string;
		phone?: string;
	};
}

/**
 * AP appointment webhook handler for bi-directional synchronization
 * Auto-detects create vs update operations based on existing records in local database
 * Location is retrieved from config file instead of URL parameter
 *
 * @param c - Hono context
 * @returns Response
 */
export async function apAppointmentHandler(
	c: Context,
): Promise<Response> {
	const requestId = crypto.randomUUID();

	try {
		const body = await c.req.json();
		logDebug("📅 AP Appointment Webhook received:", body);

		// Validate webhook structure
		if (!isValidAPWebhookEvent(body)) {
			return c.json(
				{
					error: "Invalid webhook event structure",
					requestId,
				},
				400,
			);
		}

		const event = body as APWebhookEvent;
		const { calendar, contact_id: contactId } = event.data;

		if (!calendar) {
			return c.json(
				{
					error: "Calendar data not found",
					requestId,
				},
				400,
			);
		}

		// Skip if created/updated by third party (prevents loops)
		if (
			calendar?.created_by_meta?.source === "third_party" ||
			calendar?.last_updated_by_meta?.source === "third_party"
		) {
			return c.json(
				{
					message: "Appointment created/updated by third party, skipping",
					requestId,
					skipped: true,
				},
				200,
			);
		}

		if (!contactId) {
			return c.json(
				{
					error: "Contact ID not found",
					requestId,
				},
				400,
			);
		}

		// Convert calendar data to standard appointment format
		// Cast to WebhookCalendar to access status properties
		const calendarData = calendar as unknown as WebhookCalendar;
		const appointmentPayload: GetAPAppointmentType = {
			id: calendar?.appointmentId || "",
			contactId: contactId,
			startTime: calendar?.startTime || "",
			endTime: calendar?.endTime || "",
			title: calendar?.title || "Appointment from AP",
			appointmentStatus: calendarData?.status || "scheduled",
			calendarId: calendar?.id || "",
			locationId: "", // Will be set by processor
			assignedUserId: null,
			address: null,
			appoinmentStatus:
				calendarData?.appoinmentStatus || calendarData?.status || "scheduled",
		};

		// Auto-detect operation type by checking if appointment exists in local database
		const db = getDb();
		const appointmentId = calendar?.appointmentId;
		if (!appointmentId) {
			return c.json(
				{
					error: "Appointment ID not found in calendar data",
					requestId,
				},
				400,
			);
		}

		const existingAppointment = await db
			.select()
			.from(appointment)
			.where(eq(appointment.apId, appointmentId))
			.limit(1);

		let operationType: "create" | "update" | "delete";

		if (event.type === "appointment_deleted") {
			operationType = "delete";
		} else if (existingAppointment.length > 0) {
			operationType = "update";
		} else {
			operationType = "create";
		}

		// Create webhook context
		const context: WebhookContext = {
			event: {
				event:
					operationType === "create"
						? "EntityWasCreated"
						: operationType === "update"
							? "EntityWasUpdated"
							: "EntityWasDeleted",
				model: "Appointment",
				id: parseInt(calendar.appointmentId),
				payload: calendar,
			},
			processedAt: new Date(),
			requestId,
		};

		let result: { success?: boolean; message: string; skipped?: boolean };

		// Route to appropriate processor based on auto-detected operation
		switch (operationType) {
			case "create":
				result = await processAPAppointmentCreate(appointmentPayload, context);
				break;

			case "update":
				result = await processAPAppointmentUpdate(appointmentPayload, context);
				break;

			case "delete":
				result = await processAPAppointmentDelete(appointmentPayload, context);
				break;

			default:
				return c.json(
					{
						error: `Unknown operation type: ${operationType}`,
						requestId,
					},
					400,
				);
		}

		return c.json(
			{
				message: `AP appointment webhook processed successfully (${operationType})`,
				requestId,
				result,
				operationType,
			},
			200,
		);
	} catch (error) {
		await logError(
			"AP_APPOINTMENT_WEBHOOK_ERROR",
			error,
			{ requestId },
			"APWebhookHandler",
		);

		return c.json(
			{
				error: "Internal server error",
				requestId,
			},
			500,
		);
	}
}

/**
 * AP contact webhook handler for bi-directional synchronization
 * Auto-detects create vs update operations based on existing records in local database
 * Location is retrieved from config file instead of URL parameter
 *
 * @param c - Hono context
 * @returns Response
 */
export async function apContactHandler(c: Context): Promise<Response> {
	const requestId = crypto.randomUUID();

	try {
		const body = await c.req.json();
		logDebug("👤 AP Contact Webhook received:", body);

		// Validate webhook structure
		if (!isValidAPWebhookEvent(body)) {
			return c.json(
				{
					error: "Invalid webhook event structure",
					requestId,
				},
				400,
			);
		}

		const event = body as APWebhookEvent;
		const { contact } = event.data;

		if (!contact) {
			return c.json(
				{
					error: "Contact data not found",
					requestId,
				},
				400,
			);
		}

		// Auto-detect operation type by checking if contact exists in local database
		const db = getDb();
		const existingContact = await db
			.select()
			.from(patient)
			.where(eq(patient.apId, contact.id))
			.limit(1);

		let operationType: "create" | "update" | "delete";

		if (event.type === "contact_deleted") {
			operationType = "delete";
		} else if (existingContact.length > 0) {
			operationType = "update";
		} else {
			operationType = "create";
		}

		// Create webhook context
		const context: WebhookContext = {
			event: {
				event:
					operationType === "create"
						? "EntityWasCreated"
						: operationType === "update"
							? "EntityWasUpdated"
							: "EntityWasDeleted",
				model: "Patient",
				id: parseInt(contact.id),
				payload: contact,
			},
			processedAt: new Date(),
			requestId,
		};

		let result: { success?: boolean; message: string; skipped?: boolean };

		// Route to appropriate processor based on auto-detected operation
		switch (operationType) {
			case "create":
				result = await processAPContactCreate(contact, context);
				break;

			case "update":
				result = await processAPContactUpdate(contact, context);
				break;

			case "delete":
				result = await processAPContactDelete(contact, context);
				break;

			default:
				return c.json(
					{
						error: `Unknown operation type: ${operationType}`,
						requestId,
					},
					400,
				);
		}

		return c.json(
			{
				message: `AP contact webhook processed successfully (${operationType})`,
				requestId,
				result,
				operationType,
			},
			200,
		);
	} catch (error) {
		await logError(
			"AP_CONTACT_WEBHOOK_ERROR",
			error,
			{ requestId },
			"APWebhookHandler",
		);

		return c.json(
			{
				error: "Internal server error",
				requestId,
			},
			500,
		);
	}
}

/**
 * Type guard to validate AP webhook event structure
 *
 * @param event - Unknown event data
 * @returns True if valid AP webhook event
 */
function isValidAPWebhookEvent(event: unknown): event is APWebhookEvent {
	return (
		event !== null &&
		typeof event === "object" &&
		"type" in event &&
		"data" in event &&
		typeof (event as Record<string, unknown>).type === "string" &&
		typeof (event as Record<string, unknown>).data === "object" &&
		(event as Record<string, unknown>).data !== null
	);
}
