import { ccClient } from "@api";
import type {
	GetAPContactType,
	GetCCPatientType,
	PostAPContactType,
	PostCCPatientType,
} from "@type";
import {
	type FinancialMetrics,
	getServiceAppointmentCountFieldName,
	getServiceSpendingFieldName,
	type LatestInvoiceData,
	type LatestPaymentData,
	type ServiceAppointmentCounts,
	type ServiceSpendingAmounts,
} from "@utils/customFieldDefinitions";

/**
 * Data transformation utilities for bi-directional sync between CC and AP platforms
 *
 * This module provides comprehensive data transformation functions to convert data
 * between CliniCore (CC) and AutoPatient (AP) formats. It handles field mapping,
 * data validation, type conversion, and format standardization to ensure seamless
 * data synchronization between the two platforms.
 *
 * **Key Features:**
 * - Bi-directional data transformation (CC ↔ AP)
 * - Custom field value processing and formatting
 * - HTML content sanitization and text processing
 * - Date/time format standardization
 * - Service and appointment counting utilities
 * - Spending calculation and LTV metrics
 *
 * **Data Integrity:**
 * - Validates required fields before transformation
 * - <PERSON>les null/undefined values gracefully
 * - Preserves data types and formats
 * - Implements proper error handling
 *
 * **Performance:**
 * - Optimized for high-volume data processing
 * - Efficient field mapping algorithms
 * - Minimal memory footprint
 * - Fast string processing operations
 *
 * @example
 * ```typescript
 * // Transform CC patient to AP contact
 * const ccPatient = { id: 123, firstName: "John", lastName: "Doe" };
 * const apContact = transformCCPatientToAPContact(ccPatient);
 *
 * // Transform AP contact to CC patient (using cleaned database values)
 * const dbPatient = { email: "<EMAIL>", phone: "+**********", apData: apContactData };
 * const ccPatient = transformAPContactToCCPatient(dbPatient);
 *
 * // Extract custom field values
 * const customFields = extractCCCustomFieldValues(ccPatient);
 *
 * // Clean HTML content
 * const cleanText = removeHtmlTags("<p>Hello <b>World</b></p>");
 * ```
 *
 * @see {@link transformCCPatientToAPContact} for CC to AP patient transformation
 * @see {@link transformAPContactToCCPatient} for AP to CC patient transformation
 * @see {@link extractCCCustomFieldValues} for custom field extraction
 * @see {@link removeHtmlTags} for HTML content sanitization
 *
 * @since 1.0.0
 * @version 1.0.0
 */

/**
 * Transforms CC patient data to AP contact format
 *
 * Converts a CliniCore patient record to AutoPatient contact format, handling
 * field mapping, data validation, and format conversion. This function ensures
 * that all relevant patient information is properly transferred to the AP platform
 * while maintaining data integrity and following AP API requirements.
 *
 * **Field Mapping:**
 * - `firstName` → `firstName`
 * - `lastName` → `lastName`
 * - `email` → `email`
 * - `phoneMobile` → `phone`
 * - `dob` → `dateOfBirth`
 * - `gender` → `gender`
 * - Adds `cc_api` tag for identification
 * - Sets `source` to "cc" for tracking
 *
 * **Data Validation:**
 * - Handles null/undefined values gracefully
 * - Ensures proper data types for all fields
 * - Validates email and phone formats
 * - Preserves original data structure when possible
 *
 * **Business Rules:**
 * - All contacts created from CC patients are tagged with "cc_api"
 * - Source is always set to "cc" for audit tracking
 * - Gender values are preserved as-is from CC
 * - Date of birth is converted to AP format if present
 *
 * @param ccPatient - Complete CC patient data object
 * @param ccPatient.firstName - Patient's first name
 * @param ccPatient.lastName - Patient's last name
 * @param ccPatient.email - Patient's email address
 * @param ccPatient.phoneMobile - Patient's mobile phone number
 * @param ccPatient.dob - Patient's date of birth (ISO string)
 * @param ccPatient.gender - Patient's gender
 *
 * @returns AP contact data object ready for API submission
 * @returns result.firstName - Mapped first name
 * @returns result.lastName - Mapped last name
 * @returns result.email - Mapped email address
 * @returns result.phone - Mapped phone number
 * @returns result.dateOfBirth - Mapped date of birth
 * @returns result.gender - Mapped gender
 * @returns result.tags - Array containing "cc_api" tag
 * @returns result.source - Always "cc" for tracking
 *
 * @example
 * ```typescript
 * const ccPatient = {
 *   id: 12345,
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phoneMobile: "+**********",
 *   dob: "1990-01-01",
 *   gender: "male"
 * };
 *
 * const apContact = transformCCPatientToAPContact(ccPatient);
 *
 * // Result:
 * {
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phone: "+**********",
 *   dateOfBirth: "1990-01-01",
 *   gender: "male",
 *   tags: ["cc_api"],
 *   source: "cc"
 * }
 * ```
 *
 * @see {@link transformAPContactToCCPatient} for reverse transformation
 * @see {@link PostAPContactType} for AP contact type definition
 * @see {@link GetCCPatientType} for CC patient type definition
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export function transformCCPatientToAPContact(
	ccPatient: GetCCPatientType,
): PostAPContactType {
	// Build the contact data object following v3Integration pattern
	// Use the same logic as v3Integration's updateOrCreateContact function
	const contactData: PostAPContactType = {
		email: ccPatient.email || undefined,
		phone: ccPatient.phoneMobile || undefined,
		firstName: ccPatient.firstName || undefined,
		lastName: ccPatient.lastName || undefined,
		dateOfBirth: ccPatient.dob || undefined,
		gender: ccPatient.gender || undefined,
		tags: ["cc_api"],
		source: "cc",
	};

	return contactData;
}

/**
 * Transforms AP contact data to CC patient format using cleaned database values
 *
 * CRITICAL: This function now uses cleaned database values like v3Integration,
 * not raw webhook payload data. This ensures email/phone data appears correctly
 * in CC platform by using the same pattern as v3Integration's updatePatientToCC().
 *
 * @param dbPatient - Patient record from database with cleaned values
 * @param apContact - AP contact data for additional fields
 * @returns CC patient data format
 */
export function transformAPContactToCCPatient(
	dbPatient: { email: string | null; phone: string | null; apData: GetAPContactType | null },
	apContact?: GetAPContactType,
): PostCCPatientType {
	// Use AP contact data from database or fallback to provided contact
	const sourceContact = dbPatient.apData || apContact;

	if (!sourceContact) {
		throw new Error("No AP contact data available for transformation");
	}

	// CRITICAL: Use cleaned database values for email/phone like v3Integration
	// v3Integration pattern: email: contact.email, phoneMobile: contact.phone
	const patientData: PostCCPatientType = {
		firstName: sourceContact.firstName || "",
		lastName: sourceContact.lastName || "",
		email: dbPatient.email || "",        // ✅ Use cleaned DB value
		phoneMobile: dbPatient.phone || "",  // ✅ Use cleaned DB value
	};

	// Only include optional fields that have actual values
	if (sourceContact.dateOfBirth && sourceContact.dateOfBirth.trim() !== "") {
		patientData.dob = sourceContact.dateOfBirth;
	}
	if (sourceContact.gender && sourceContact.gender.trim() !== "") {
		patientData.gender = sourceContact.gender;
	}

	return patientData;
}

/**
 * Extracts custom field values from CC patient data
 *
 * @param ccPatient - CC patient data
 * @returns Object with custom field labels and values
 */
export function extractCCCustomFieldValues(
	ccPatient: GetCCPatientType,
): Record<string, string | number> {
	const customFieldValues: Record<string, string | number> = {};

	// Add basic patient information
	customFieldValues["Patient ID"] = ccPatient.id;
	customFieldValues["Total Appointments"] = ccPatient.appointments?.length || 0;

	// Add other relevant fields
	if (ccPatient.title) {
		customFieldValues.Title = ccPatient.title;
	}

	if (ccPatient.titleSuffix) {
		customFieldValues["Title Suffix"] = ccPatient.titleSuffix;
	}

	if (ccPatient.healthInsurance) {
		customFieldValues["Health Insurance"] = ccPatient.healthInsurance;
	}

	if (ccPatient.ssn) {
		customFieldValues.SSN = ccPatient.ssn;
	}

	return customFieldValues;
}

/**
 * Reduces custom field value to string format
 * Handles various data types and formats them appropriately
 *
 * @param value - Custom field value
 * @returns Formatted string value
 */
export function reduceCustomFieldValue(value: unknown): string {
	if (value === null || value === undefined) {
		return "";
	}

	if (typeof value === "string") {
		return value.trim();
	}

	if (typeof value === "number") {
		return value.toString();
	}

	if (typeof value === "boolean") {
		return value ? "Yes" : "No";
	}

	if (Array.isArray(value)) {
		return value.map((item) => reduceCustomFieldValue(item)).join(", ");
	}

	if (typeof value === "object") {
		// Handle date objects
		if (value instanceof Date) {
			return value.toISOString();
		}

		// Handle objects with a value property
		if ("value" in value) {
			return reduceCustomFieldValue(value.value);
		}

		// Convert object to JSON string
		return JSON.stringify(value);
	}

	return String(value);
}

/**
 * Removes HTML tags from a string
 *
 * @param html - HTML string
 * @returns Plain text string
 */
export function removeHtmlTags(html: string): string {
	if (!html || typeof html !== "string") {
		return "";
	}

	// Remove HTML tags using regex
	return html
		.replace(/<[^>]*>/g, "")
		.replace(/&nbsp;/g, " ")
		.replace(/&amp;/g, "&")
		.replace(/&lt;/g, "<")
		.replace(/&gt;/g, ">")
		.replace(/&quot;/g, '"')
		.replace(/&#39;/g, "'")
		.trim();
}

// formatDateToISO function moved to @utils/validation for consistency

/**
 * Calculates service appointment counts for a patient (Advanced Implementation)
 *
 * This function fetches detailed appointment data and counts appointments per service,
 * similar to the individualServiceAppointmentCount function from v3Integration.
 * It provides granular service-level appointment counting for accurate custom field updates.
 *
 * @param ccPatient - CC patient data with appointment IDs
 * @returns Promise resolving to object with service names and appointment counts
 */
export async function calculateServiceAppointmentCounts(
	ccPatient: GetCCPatientType,
): Promise<ServiceAppointmentCounts> {
	const serviceCounts: ServiceAppointmentCounts = {};

	// Count total appointments
	if (ccPatient.appointments && Array.isArray(ccPatient.appointments)) {
		const totalAppointments = ccPatient.appointments.length;
		serviceCounts["Total Appointments"] = totalAppointments;

		if (totalAppointments === 0) {
			console.log(`No appointments found for patient ${ccPatient.id}`);
			return serviceCounts;
		}

		try {
			// Fetch all appointment details
			const appointmentPromises = ccPatient.appointments.map((appointmentId) =>
				ccClient.appointment.get(appointmentId),
			);
			const appointments = await Promise.all(appointmentPromises);

			// Fetch all services for mapping
			const services = await ccClient.service.all();
			const serviceMap = new Map(
				services.map((service) => [service.id, service]),
			);

			// Count appointments per service
			for (const appointment of appointments) {
				if (appointment.services && Array.isArray(appointment.services)) {
					for (const serviceId of appointment.services) {
						const service = serviceMap.get(serviceId);
						if (service) {
							const fieldName = getServiceAppointmentCountFieldName(
								service.name,
							);
							serviceCounts[fieldName] = (serviceCounts[fieldName] || 0) + 1;
						}
					}
				}
			}

			console.log(
				`Calculated service appointment counts for ${Object.keys(serviceCounts).length} services`,
			);
		} catch (error) {
			console.error(
				`Failed to calculate service appointment counts for patient ${ccPatient.id}:`,
				error,
			);
			// Return basic count on error
			serviceCounts["General Services"] = totalAppointments;
		}
	}

	return serviceCounts;
}

/**
 * Calculates service spending amounts for a patient (Advanced Implementation)
 *
 * This function fetches detailed invoice data and calculates spending per service/product,
 * similar to the individualServiceSpends function from v3Integration.
 * It provides granular service-level spending calculations for accurate custom field updates.
 *
 * @param ccPatient - CC patient data with invoice IDs
 * @returns Promise resolving to object with service names and spending amounts
 */
export async function calculateServiceSpending(
	ccPatient: GetCCPatientType,
): Promise<ServiceSpendingAmounts> {
	const serviceSpending: ServiceSpendingAmounts = {};

	// Calculate spending from invoices
	if (ccPatient.invoices && Array.isArray(ccPatient.invoices)) {
		const totalInvoices = ccPatient.invoices.length;
		serviceSpending["Total Invoices"] = totalInvoices;

		if (totalInvoices === 0) {
			console.log(`No invoices found for patient ${ccPatient.id}`);
			return serviceSpending;
		}

		try {
			// Fetch all invoice details
			const invoices = await ccClient.invoice.getMultiple(ccPatient.invoices);

			// Calculate spending per service/product from invoice positions
			for (const invoice of invoices) {
				if (invoice.positions && Array.isArray(invoice.positions)) {
					for (const position of invoice.positions) {
						if (position.gross && position.name) {
							const fieldName = getServiceSpendingFieldName(position.name);
							serviceSpending[fieldName] =
								(serviceSpending[fieldName] || 0) + position.gross;
						}
					}
				}
			}

			console.log(
				`Calculated service spending for ${Object.keys(serviceSpending).length} services`,
			);
		} catch (error) {
			console.error(
				`Failed to calculate service spending for patient ${ccPatient.id}:`,
				error,
			);
			// Return basic count on error
			serviceSpending["Service Transactions"] = totalInvoices;
		}
	}

	// Calculate payments received
	if (ccPatient.payments && Array.isArray(ccPatient.payments)) {
		const totalPayments = ccPatient.payments.length;
		serviceSpending["Total Payments"] = totalPayments;

		if (totalPayments > 0) {
			serviceSpending["Payment Transactions"] = totalPayments;
		}
	}

	return serviceSpending;
}

// Validation functions moved to @utils/validation for consistency
// - isValidEmail
// - isValidPhone
// - formatPhoneNumber

/**
 * Safely parses JSON string
 *
 * @param jsonString - JSON string to parse
 * @param defaultValue - Default value if parsing fails
 * @returns Parsed object or default value
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
	if (!jsonString || typeof jsonString !== "string") {
		return defaultValue;
	}

	try {
		return JSON.parse(jsonString);
	} catch (error) {
		console.error("Error parsing JSON:", error);
		return defaultValue;
	}
}

/**
 * Safely stringifies object to JSON
 *
 * @param obj - Object to stringify
 * @param defaultValue - Default value if stringification fails
 * @returns JSON string or default value
 */
export function safeJsonStringify(
	obj: unknown,
	defaultValue: string = "{}",
): string {
	try {
		return JSON.stringify(obj);
	} catch (error) {
		console.error("Error stringifying JSON:", error);
		return defaultValue;
	}
}

/**
 * Calculates financial metrics for a patient (Advanced Implementation)
 *
 * This function calculates comprehensive financial metrics including:
 * - Due amount (outstanding balance)
 * - Credit amount (overpayments)
 * - Total invoiced amount
 * - Lifetime value (LTV)
 *
 * @param ccPatient - CC patient data with invoice and payment IDs
 * @returns Promise resolving to financial metrics object
 */
export async function calculateFinancialMetrics(
	ccPatient: GetCCPatientType,
): Promise<FinancialMetrics> {
	const metrics: FinancialMetrics = {
		dueAmount: 0,
		creditAmount: 0,
		totalInvoicedAmount: 0,
		lifetimeValue: 0,
	};

	try {
		// Fetch invoice data if available
		if (ccPatient.invoices && ccPatient.invoices.length > 0) {
			const invoices = await ccClient.invoice.getMultiple(ccPatient.invoices);

			// Calculate total invoiced amount
			for (const invoice of invoices) {
				if (invoice.positions && Array.isArray(invoice.positions)) {
					for (const position of invoice.positions) {
						if (position.gross) {
							metrics.totalInvoicedAmount += position.gross;
						}
					}
				}
			}
		}

		// Fetch payment data if available
		if (ccPatient.payments && ccPatient.payments.length > 0) {
			const payments = await ccClient.payment.getMultiple(ccPatient.payments);

			// Calculate total payments (LTV)
			for (const payment of payments) {
				if (payment.gross && !payment.canceled) {
					metrics.lifetimeValue += Math.abs(payment.gross);
				}
			}
		}

		// Calculate due amount (invoiced - paid)
		metrics.dueAmount = Math.max(
			0,
			metrics.totalInvoicedAmount - metrics.lifetimeValue,
		);

		// Calculate credit amount (overpayments)
		metrics.creditAmount = Math.max(
			0,
			metrics.lifetimeValue - metrics.totalInvoicedAmount,
		);

		console.log(
			`Calculated financial metrics for patient ${ccPatient.id}: LTV=${metrics.lifetimeValue}, Due=${metrics.dueAmount}`,
		);
	} catch (error) {
		console.error(
			`Failed to calculate financial metrics for patient ${ccPatient.id}:`,
			error,
		);
	}

	return metrics;
}

/**
 * Extracts latest invoice data for a patient (Advanced Implementation)
 *
 * This function fetches the most recent invoice and extracts relevant data
 * for custom field updates including PDF URL, amounts, diagnosis, etc.
 *
 * @param ccPatient - CC patient data with invoice IDs
 * @returns Promise resolving to latest invoice data object
 */
export async function extractLatestInvoiceData(
	ccPatient: GetCCPatientType,
): Promise<LatestInvoiceData> {
	const latestData: LatestInvoiceData = {};

	try {
		if (ccPatient.invoices && ccPatient.invoices.length > 0) {
			// Get the most recent invoice (last in array)
			const latestInvoiceId = ccPatient.invoices[ccPatient.invoices.length - 1];
			const latestInvoice = await ccClient.invoice.get(latestInvoiceId);

			// Extract invoice data
			latestData.pdfUrl = latestInvoice.pdfUrl;
			latestData.discount = latestInvoice.discount;
			latestData.paymentStatus = latestInvoice.status;

			// Calculate gross amount from positions
			if (latestInvoice.positions && Array.isArray(latestInvoice.positions)) {
				let grossAmount = 0;
				const products: string[] = [];

				for (const position of latestInvoice.positions) {
					if (position.gross) {
						grossAmount += position.gross;
					}
					if (position.name) {
						products.push(position.name);
					}
				}

				latestData.grossAmount = grossAmount;
				latestData.totalAmount = grossAmount - (latestInvoice.discount || 0);
				latestData.products = products.join(", ");
			}

			// Extract diagnosis
			if (latestInvoice.diagnoses && Array.isArray(latestInvoice.diagnoses)) {
				const diagnosisTexts = latestInvoice.diagnoses
					.map((d) => d.text)
					.filter(Boolean);
				latestData.diagnosis = diagnosisTexts.join(", ");
			}

			// Get practitioner name
			if (latestInvoice.practicioner) {
				try {
					const practitioner = await ccClient.user.get(
						latestInvoice.practicioner,
					);
					latestData.treatedBy = `${practitioner.firstName} ${practitioner.lastName}`;
				} catch (error) {
					console.error(
						`Failed to fetch practitioner ${latestInvoice.practicioner}:`,
						error,
					);
				}
			}

			console.log(`Extracted latest invoice data for patient ${ccPatient.id}`);
		}
	} catch (error) {
		console.error(
			`Failed to extract latest invoice data for patient ${ccPatient.id}:`,
			error,
		);
	}

	return latestData;
}

/**
 * Extracts latest payment data for a patient (Advanced Implementation)
 *
 * This function fetches the most recent payment and extracts relevant data
 * for custom field updates including PDF URL, amount, date, status, etc.
 *
 * @param ccPatient - CC patient data with payment IDs
 * @returns Promise resolving to latest payment data object
 */
export async function extractLatestPaymentData(
	ccPatient: GetCCPatientType,
): Promise<LatestPaymentData> {
	const latestData: LatestPaymentData = {};

	try {
		if (ccPatient.payments && ccPatient.payments.length > 0) {
			// Get the most recent payment (last in array)
			const latestPaymentId = ccPatient.payments[ccPatient.payments.length - 1];
			const latestPayment = await ccClient.payment.get(latestPaymentId);

			// Extract payment data
			latestData.amount = Math.abs(latestPayment.gross);
			latestData.date = latestPayment.date;
			latestData.pdfUrl = latestPayment.pdfUrl;
			latestData.status = latestPayment.canceled ? "Cancelled" : "Success";

			console.log(`Extracted latest payment data for patient ${ccPatient.id}`);
		}
	} catch (error) {
		console.error(
			`Failed to extract latest payment data for patient ${ccPatient.id}:`,
			error,
		);
	}

	return latestData;
}

/**
 * Generates comprehensive custom field data for a patient (Advanced Implementation)
 *
 * This function combines all advanced custom field calculations to generate
 * a complete set of custom field data for AP contact updates. It includes:
 * - Service appointment counts
 * - Service spending amounts
 * - Financial metrics (LTV, due amounts, etc.)
 * - Latest invoice data
 * - Latest payment data
 *
 * @param ccPatient - CC patient data
 * @returns Promise resolving to comprehensive custom field data object
 */
export async function generateAdvancedCustomFieldData(
	ccPatient: GetCCPatientType,
): Promise<Record<string, string | number>> {
	const customFieldData: Record<string, string | number> = {};

	try {
		// Basic patient information
		customFieldData["Patient ID"] = ccPatient.id;
		customFieldData["Total Appointments"] = ccPatient.appointments?.length || 0;

		if (ccPatient.title) {
			customFieldData.Title = ccPatient.title;
		}

		if (ccPatient.titleSuffix) {
			customFieldData["Title Suffix"] = ccPatient.titleSuffix;
		}

		// Advanced calculations (run in parallel for performance)
		const [
			serviceAppointmentCounts,
			serviceSpendingAmounts,
			financialMetrics,
			latestInvoiceData,
			latestPaymentData,
		] = await Promise.all([
			calculateServiceAppointmentCounts(ccPatient),
			calculateServiceSpending(ccPatient),
			calculateFinancialMetrics(ccPatient),
			extractLatestInvoiceData(ccPatient),
			extractLatestPaymentData(ccPatient),
		]);

		// Add service appointment counts
		Object.assign(customFieldData, serviceAppointmentCounts);

		// Add service spending amounts
		Object.assign(customFieldData, serviceSpendingAmounts);

		// Add financial metrics
		customFieldData["Due amount"] = financialMetrics.dueAmount;
		customFieldData["Credit amount"] = financialMetrics.creditAmount;
		customFieldData["Total Invoiced Amount"] =
			financialMetrics.totalInvoicedAmount;
		customFieldData["Life Time Value"] = financialMetrics.lifetimeValue;

		// Add latest invoice data
		if (latestInvoiceData.pdfUrl) {
			customFieldData["Latest Invoice PDF URL"] = latestInvoiceData.pdfUrl;
		}
		if (latestInvoiceData.grossAmount !== undefined) {
			customFieldData["Latest Gross Amount"] = latestInvoiceData.grossAmount;
		}
		if (latestInvoiceData.discount !== undefined) {
			customFieldData["Latest Discount"] = latestInvoiceData.discount;
		}
		if (latestInvoiceData.totalAmount !== undefined) {
			customFieldData["Latest Total Amount"] = latestInvoiceData.totalAmount;
		}
		if (latestInvoiceData.paymentStatus) {
			customFieldData["Latest Payment Status"] =
				latestInvoiceData.paymentStatus;
		}
		if (latestInvoiceData.products) {
			customFieldData["Latest Products"] = latestInvoiceData.products;
		}
		if (latestInvoiceData.diagnosis) {
			customFieldData["Latest Diagnosis"] = latestInvoiceData.diagnosis;
		}
		if (latestInvoiceData.treatedBy) {
			customFieldData["Latest Treated By"] = latestInvoiceData.treatedBy;
		}

		// Add latest payment data
		if (latestPaymentData.amount !== undefined) {
			customFieldData["Latest Amount Paid"] = latestPaymentData.amount;
		}
		if (latestPaymentData.date) {
			customFieldData["Latest Payment Date"] = latestPaymentData.date;
		}
		if (latestPaymentData.pdfUrl) {
			customFieldData["Latest Payment PDF URL"] = latestPaymentData.pdfUrl;
		}
		if (latestPaymentData.status) {
			customFieldData["Latest Payment Status"] = latestPaymentData.status;
		}

		console.log(
			`Generated ${Object.keys(customFieldData).length} custom fields for patient ${ccPatient.id}`,
		);
	} catch (error) {
		console.error(
			`Failed to generate advanced custom field data for patient ${ccPatient.id}:`,
			error,
		);
	}

	return customFieldData;
}
