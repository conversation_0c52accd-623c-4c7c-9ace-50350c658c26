/**
 * Context-Aware Sync Utilities for DermaCare Bi-Directional Sync Service
 *
 * This module provides intelligent sync decision logic that determines the appropriate
 * action to take based on existing database state. It implements the v3Integration
 * pattern of early database checks followed by context-aware API decisions.
 *
 * **Key Features:**
 * - Smart sync action determination based on existing platform IDs
 * - Prevents unnecessary API calls for already-synced records
 * - Implements search-then-create logic for missing records
 * - Supports both patient and appointment sync scenarios
 * - Maintains data consistency in bi-directional sync
 *
 * **Sync Action Types:**
 * - `skip`: Record already synced to target platform, no action needed
 * - `create`: Create new record in target platform
 * - `update`: Update existing record in target platform
 * - `search_then_create`: Search target platform first, create if not found
 *
 * @example
 * ```typescript
 * // Determine patient sync action
 * const action = determinePatientSyncAction(dbPatient, 'cc', 'create');
 * 
 * if (action === 'skip') {
 *   return { success: true, message: 'Already synced' };
 * }
 * 
 * // Execute context-aware sync based on determined action
 * const result = await executePatientSync(dbPatient, payload, action);
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import type { patient, appointment } from "@database/schema";
import type { GetCCPatientType, GetAPContactType, GetCCAppointmentType, GetAPAppointmentType } from "@type";

/**
 * Sync action types that determine how to handle platform synchronization
 */
export type SyncAction = 'skip' | 'create' | 'update' | 'search_then_create';

/**
 * Platform identifiers for sync operations
 */
export type SyncPlatform = 'cc' | 'ap';

/**
 * Operation types for sync context
 */
export type SyncOperation = 'create' | 'update';

/**
 * Patient record type from database schema
 */
export type PatientRecord = typeof patient.$inferSelect;

/**
 * Appointment record type from database schema
 */
export type AppointmentRecord = typeof appointment.$inferSelect;

/**
 * Context information for sync decisions
 */
export interface SyncContext {
	/** Database patient record */
	dbPatient: PatientRecord;
	/** Determined sync action */
	action: SyncAction;
	/** Target platform for sync */
	targetPlatform: SyncPlatform;
	/** Type of operation being performed */
	operation: SyncOperation;
	/** Reason for the sync decision */
	reason: string;
}

/**
 * Determines the appropriate sync action for patient data based on database state
 *
 * This function implements the core logic for context-aware sync decisions.
 * It analyzes the existing patient record to determine whether to skip, create,
 * update, or search-then-create in the target platform.
 *
 * **Decision Logic:**
 * - **CC → AP Sync**: Check if patient has `apId`
 *   - Has `apId`: Skip (create) or Update (update operation)
 *   - No `apId`: Search-then-create
 * - **AP → CC Sync**: Check if patient has `ccId`
 *   - Has `ccId`: Skip (create) or Update (update operation)
 *   - No `ccId`: Search-then-create
 *
 * **Skip Conditions:**
 * - Create operation + target platform ID exists = Already synced
 * - Update operation + target platform ID exists = Proceed with update
 * - Update operation + no target platform ID = Search-then-create
 *
 * @param dbPatient - Patient record from local database
 * @param source - Source platform ('cc' for CliniCore, 'ap' for AutoPatient)
 * @param operation - Type of operation ('create' or 'update')
 * @returns Sync action to take and context information
 *
 * @example
 * ```typescript
 * // CC patient create - check if already synced to AP
 * const context = determinePatientSyncAction(dbPatient, 'cc', 'create');
 * if (context.action === 'skip') {
 *   console.log(`Patient already synced: ${context.reason}`);
 *   return;
 * }
 *
 * // AP contact update - determine if need to create or update in CC
 * const context = determinePatientSyncAction(dbPatient, 'ap', 'update');
 * if (context.action === 'search_then_create') {
 *   console.log(`Need to search CC first: ${context.reason}`);
 * }
 * ```
 */
export function determinePatientSyncAction(
	dbPatient: PatientRecord,
	source: SyncPlatform,
	operation: SyncOperation,
): SyncContext {
	const targetPlatform: SyncPlatform = source === 'cc' ? 'ap' : 'cc';
	const targetIdField = targetPlatform === 'ap' ? 'apId' : 'ccId';
	const hasTargetId = dbPatient[targetIdField] !== null;

	let action: SyncAction;
	let reason: string;

	if (operation === 'create') {
		if (hasTargetId) {
			action = 'skip';
			reason = `Patient already synced to ${targetPlatform.toUpperCase()}. ${targetIdField}: ${dbPatient[targetIdField]}`;
		} else {
			action = 'search_then_create';
			reason = `Patient not synced to ${targetPlatform.toUpperCase()}, will search then create if needed`;
		}
	} else { // operation === 'update'
		if (hasTargetId) {
			action = 'update';
			reason = `Patient exists in ${targetPlatform.toUpperCase()}, will update. ${targetIdField}: ${dbPatient[targetIdField]}`;
		} else {
			action = 'search_then_create';
			reason = `Patient not synced to ${targetPlatform.toUpperCase()}, will search then create if needed`;
		}
	}

	return {
		dbPatient,
		action,
		targetPlatform,
		operation,
		reason,
	};
}

/**
 * Determines the appropriate sync action for appointment data based on patient and appointment state
 *
 * This function handles appointment sync decisions by first checking the patient's
 * sync state, then determining the appropriate action for the appointment itself.
 *
 * **Prerequisites:**
 * - Patient must be synced to target platform before appointment sync
 * - If patient is not synced, appointment sync should be deferred
 *
 * **Decision Logic:**
 * 1. Check if patient is synced to target platform
 * 2. If patient not synced, return action to sync patient first
 * 3. If patient synced, check appointment sync state
 * 4. Determine create/update/skip based on appointment existence
 *
 * @param dbPatient - Patient record from local database
 * @param dbAppointment - Appointment record from local database (null if not exists)
 * @param source - Source platform ('cc' for CliniCore, 'ap' for AutoPatient)
 * @param operation - Type of operation ('create' or 'update')
 * @returns Sync context with action and reasoning
 *
 * @example
 * ```typescript
 * // Check appointment sync for CC → AP
 * const context = determineAppointmentSyncAction(dbPatient, dbAppointment, 'cc', 'create');
 * 
 * if (context.action === 'skip') {
 *   console.log('Patient not synced, cannot sync appointment');
 *   return;
 * }
 * ```
 */
export function determineAppointmentSyncAction(
	dbPatient: PatientRecord,
	dbAppointment: AppointmentRecord | null,
	source: SyncPlatform,
	operation: SyncOperation,
): SyncContext & { appointmentAction: SyncAction; patientSyncRequired: boolean } {
	const targetPlatform: SyncPlatform = source === 'cc' ? 'ap' : 'cc';
	const patientTargetIdField = targetPlatform === 'ap' ? 'apId' : 'ccId';
	const hasPatientTargetId = dbPatient[patientTargetIdField] !== null;

	// First check if patient is synced to target platform
	if (!hasPatientTargetId) {
		return {
			dbPatient,
			action: 'skip',
			targetPlatform,
			operation,
			reason: `Patient not synced to ${targetPlatform.toUpperCase()}, cannot sync appointment. Patient ${patientTargetIdField}: ${dbPatient[patientTargetIdField]}`,
			appointmentAction: 'skip',
			patientSyncRequired: true,
		};
	}

	// Patient is synced, now check appointment state
	const appointmentTargetIdField = targetPlatform === 'ap' ? 'apId' : 'ccId';
	let appointmentAction: SyncAction;
	let reason: string;

	if (!dbAppointment) {
		// Appointment doesn't exist in local database
		appointmentAction = 'create';
		reason = `Appointment not found locally, will create in ${targetPlatform.toUpperCase()}`;
	} else {
		// Appointment exists locally, check if synced to target
		const hasAppointmentTargetId = dbAppointment[appointmentTargetIdField] !== null;
		
		if (operation === 'create') {
			if (hasAppointmentTargetId) {
				appointmentAction = 'skip';
				reason = `Appointment already synced to ${targetPlatform.toUpperCase()}. ${appointmentTargetIdField}: ${dbAppointment[appointmentTargetIdField]}`;
			} else {
				appointmentAction = 'create';
				reason = `Appointment exists locally but not synced to ${targetPlatform.toUpperCase()}, will create`;
			}
		} else { // operation === 'update'
			if (hasAppointmentTargetId) {
				appointmentAction = 'update';
				reason = `Appointment exists in ${targetPlatform.toUpperCase()}, will update. ${appointmentTargetIdField}: ${dbAppointment[appointmentTargetIdField]}`;
			} else {
				appointmentAction = 'create';
				reason = `Appointment exists locally but not synced to ${targetPlatform.toUpperCase()}, will create`;
			}
		}
	}

	return {
		dbPatient,
		action: appointmentAction,
		targetPlatform,
		operation,
		reason,
		appointmentAction,
		patientSyncRequired: false,
	};
}

/**
 * Simple utility to check if a sync operation should be skipped
 *
 * This is a convenience function that provides a quick boolean check
 * for whether a sync operation should be skipped based on existing
 * platform IDs in the database record.
 *
 * @param dbPatient - Patient record from local database
 * @param targetPlatform - Target platform to check ('cc' or 'ap')
 * @returns True if sync should be skipped (already synced), false otherwise
 *
 * @example
 * ```typescript
 * if (shouldSkipSync(dbPatient, 'ap')) {
 *   return { success: true, message: 'Patient already synced to AP' };
 * }
 * ```
 */
export function shouldSkipSync(
	dbPatient: PatientRecord,
	targetPlatform: SyncPlatform,
): boolean {
	const targetIdField = targetPlatform === 'ap' ? 'apId' : 'ccId';
	return dbPatient[targetIdField] !== null;
}

/**
 * Validates that a patient record has the minimum required data for sync
 *
 * This function ensures that a patient record contains the necessary
 * contact information (email or phone) required for platform synchronization.
 *
 * @param dbPatient - Patient record to validate
 * @returns True if patient has required data, false otherwise
 *
 * @example
 * ```typescript
 * if (!validatePatientForSync(dbPatient)) {
 *   throw new Error('Patient missing required contact information');
 * }
 * ```
 */
export function validatePatientForSync(dbPatient: PatientRecord): boolean {
	return !!(dbPatient.email || dbPatient.phone);
}
