import { getDb } from "@database";
import { errorLogs } from "@database/schema";
import { getLoggingConfig } from "@utils/configs";
import { and, eq, gte } from "drizzle-orm";

/**
 * Comprehensive error logging utility for DermaCare sync service
 *
 * This module provides centralized error logging functionality with duplicate prevention,
 * categorization, and database persistence. It's designed to capture all errors that
 * occur during webhook processing, API calls, and data synchronization operations.
 *
 * **Key Features:**
 * - Duplicate error prevention within configurable time windows
 * - Structured error categorization and metadata capture
 * - Database persistence for error tracking and analysis
 * - Integration with monitoring and alerting systems
 * - Performance-optimized logging with minimal overhead
 *
 * **Error Categories:**
 * - `WEBHOOK_VALIDATION_ERROR` - Invalid webhook payloads
 * - `API_ERROR` - External API communication failures
 * - `DATABASE_ERROR` - Database operation failures
 * - `SYNC_ERROR` - Data synchronization failures
 * - `PROCESSING_ERROR` - General processing errors
 *
 * **Duplicate Prevention:**
 * Prevents logging the same error multiple times within a 30-second window
 * to avoid log spam and reduce database load during error storms.
 *
 * **Performance:**
 * - Asynchronous logging to prevent blocking main processing
 * - Efficient duplicate detection using database queries
 * - Minimal memory footprint for error tracking
 * - Optimized for high-volume error scenarios
 *
 * @example
 * ```typescript
 * // Log a general error
 * await logError("PROCESSING_ERROR", new Error("Something went wrong"), {
 *   userId: 123,
 *   operation: "patient_sync"
 * }, "PatientProcessor");
 *
 * // Log an API error
 * await logApiError("CC_API_ERROR", error, {
 *   endpoint: "/patients/123",
 *   method: "POST"
 * }, "CCApiClient");
 *
 * // Log a sync error
 * await logSyncError("PATIENT_SYNC_FAILED", error, 123, "CC", "PatientProcessor");
 * ```
 *
 * @see {@link logError} for general error logging
 * @see {@link logApiError} for API-specific error logging
 * @see {@link logSyncError} for sync-specific error logging
 *
 * @since 1.0.0
 * @version 1.0.0
 */

/**
 * Time window in seconds to prevent duplicate error logging
 * @constant
 */
const DUPLICATE_WINDOW_SECONDS = 30; // Prevent duplicates within 30 seconds

/**
 * Logs an error to the database with duplicate prevention
 *
 * This is the primary error logging function that captures errors with full context,
 * metadata, and stack traces. It implements intelligent duplicate prevention to
 * avoid log spam while ensuring all unique errors are captured for analysis.
 *
 * **Duplicate Prevention Logic:**
 * Checks if an error with the same type and message was logged within the last
 * 30 seconds. If found, the duplicate is skipped to prevent log spam during
 * error storms or repeated failures.
 *
 * **Error Processing:**
 * - Extracts error message and stack trace from Error objects
 * - Handles non-Error objects by converting to string
 * - Preserves all metadata for debugging and analysis
 * - Associates errors with specific services for tracking
 *
 * **Database Storage:**
 * Stores errors in the `error_logs` table with the following structure:
 * - `type`: Error category/context for filtering
 * - `message`: Human-readable error message
 * - `stack`: Full stack trace for debugging
 * - `data`: JSON metadata with additional context
 * - `created_at`: Timestamp for chronological analysis
 *
 * @param type - Error type/category for classification and filtering
 * @param error - Error object, string, or any value to be logged
 * @param metadata - Additional context data (user IDs, request data, etc.)
 * @param service - Name of the service/component that generated the error
 *
 * @returns Promise that resolves when error is logged (or skipped if duplicate)
 *
 * @throws {Error} Only if the logging operation itself fails critically
 *
 * @example
 * ```typescript
 * try {
 *   await processPatient(patientData);
 * } catch (error) {
 *   await logError("PATIENT_PROCESSING_ERROR", error, {
 *     patientId: patientData.id,
 *     operation: "create",
 *     requestId: "req_123"
 *   }, "PatientProcessor");
 *
 *   // Re-throw or handle as needed
 *   throw error;
 * }
 * ```
 *
 * @see {@link logApiError} for API-specific error logging
 * @see {@link logSyncError} for sync-specific error logging
 *
 * @since 1.0.0
 * @version 1.0.0
 */
/**
 * Formats database errors into human-readable messages
 * @param error - The database error
 * @param operation - The operation that was being performed
 * @returns Formatted error message
 */
function formatDatabaseError(error: unknown, operation?: string): string {
	const errorMessage = error instanceof Error ? error.message : String(error);

	// Check if this is a database constraint violation
	if (errorMessage.includes('duplicate key value violates unique constraint')) {
		const constraintMatch = errorMessage.match(/constraint "([^"]+)"/);
		const constraint = constraintMatch ? constraintMatch[1] : 'unknown';

		if (constraint.includes('cc_id')) {
			return `Patient with this CliniCore ID already exists in the database`;
		} else if (constraint.includes('ap_id')) {
			return `Patient with this AutoPatient ID already exists in the database`;
		} else if (constraint.includes('email')) {
			return `Patient with this email address already exists in the database`;
		}
		return `Duplicate record detected - ${constraint} constraint violated`;
	}

	// Check for foreign key violations
	if (errorMessage.includes('violates foreign key constraint')) {
		return `Referenced record does not exist - cannot create/update due to missing dependency`;
	}

	// Check for null constraint violations
	if (errorMessage.includes('null value in column') && errorMessage.includes('violates not-null constraint')) {
		const columnMatch = errorMessage.match(/column "([^"]+)"/);
		const column = columnMatch ? columnMatch[1] : 'unknown field';
		return `Required field '${column}' cannot be empty`;
	}

	// Check for data type errors
	if (errorMessage.includes('invalid input syntax')) {
		return `Invalid data format provided for database field`;
	}

	// Check for connection errors
	if (errorMessage.includes('connection') || errorMessage.includes('timeout')) {
		return `Database connection issue - please try again`;
	}

	// For insert/update operations, provide more context
	if (errorMessage.includes('Failed query: insert into')) {
		const tableMatch = errorMessage.match(/insert into "([^"]+)"/);
		const table = tableMatch ? tableMatch[1] : 'unknown table';
		return `Failed to create new record in ${table} - ${operation || 'database operation'} failed`;
	}

	if (errorMessage.includes('Failed query: update')) {
		const tableMatch = errorMessage.match(/update "([^"]+)"/);
		const table = tableMatch ? tableMatch[1] : 'unknown table';
		return `Failed to update record in ${table} - ${operation || 'database operation'} failed`;
	}

	// Return original message if no specific pattern matches
	return errorMessage;
}

export async function logError(
	type: string,
	error: unknown,
	metadata?: Record<string, unknown>,
	service?: string,
): Promise<void> {
	try {
		const rawErrorMessage = error instanceof Error ? error.message : String(error);
		const errorStack = error instanceof Error ? error.stack : undefined;

		// Format database errors for better readability
		const errorMessage = formatDatabaseError(error, metadata?.operation as string);

		// Check if this exact error was already logged recently
		const duplicateWindow = new Date(
			Date.now() - DUPLICATE_WINDOW_SECONDS * 1000,
		);

		const db = getDb();
		const existingError = await db
			.select()
			.from(errorLogs)
			.where(
				and(
					eq(errorLogs.type, type),
					eq(errorLogs.message, errorMessage),
					gte(errorLogs.createdAt, duplicateWindow),
				),
			)
			.limit(1);

		if (existingError.length > 0) {
			// Silently skip duplicate errors without console output to reduce noise
			return;
		}

		await db.insert(errorLogs).values({
			type,
			message: errorMessage,
			stack: errorStack,
			data: {
				timestamp: new Date().toISOString(),
				service: service || "Unknown",
				metadata,
				rawError: rawErrorMessage, // Store the original error message for debugging
				formattedError: errorMessage, // Store the human-readable version
				error:
					error instanceof Error
						? {
								name: error.name,
								message: error.message,
								stack: error.stack,
							}
						: error,
			},
		});

		// Log the formatted error message to console (always show errors)
		console.log(`❌ ${type}: ${errorMessage}`);

		// If this is a database error, also log some debugging info (only when debug logs are enabled)
		const showDebugLogs = getLoggingConfig("showDebugLogs");
		if (showDebugLogs && rawErrorMessage !== errorMessage && rawErrorMessage.includes('Failed query:')) {
			console.log(`🔍 Debug info: ${rawErrorMessage.substring(0, 200)}...`);
		}
	} catch (logError) {
		// If error logging fails, at least log to console
		console.error("Failed to log error to database:", logError);
		console.error("Original error:", error);
	}
}

/**
 * Log a sync error with patient/contact context
 * @param type - Error type
 * @param error - Error object
 * @param patientId - Patient/Contact ID
 * @param platform - Platform (CC or AP)
 * @param service - Service name
 */
export async function logSyncError(
	type: string,
	error: unknown,
	patientId: string | number,
	platform: "CC" | "AP",
	service: string,
): Promise<void> {
	// Determine the operation type from the error type
	let operation = "sync operation";
	if (type.includes("CREATE")) {
		operation = `creating ${platform} record`;
	} else if (type.includes("UPDATE")) {
		operation = `updating ${platform} record`;
	} else if (type.includes("DELETE")) {
		operation = `deleting ${platform} record`;
	} else if (type.includes("SYNC")) {
		operation = `syncing data between CC and AP`;
	}

	await logError(
		type,
		error,
		{
			patientId,
			platform,
			operation,
			syncDirection: `${platform} -> ${platform === "CC" ? "AP" : "CC"}`,
		},
		service,
	);
}

/**
 * Log an API error
 * @param type - Error type
 * @param error - Error object
 * @param endpoint - API endpoint
 * @param method - HTTP method
 * @param service - Service name
 */
export async function logApiError(
	type: string,
	error: unknown,
	endpoint: string,
	method: string,
	service: string,
): Promise<void> {
	await logError(
		type,
		error,
		{
			endpoint,
			method,
			apiError: true,
		},
		service,
	);
}
