/**
 * Legacy socket event type (for reference) - removed as it's not used
 */

/**
 * New webhook event structure
 * Replaces socket events with standardized webhook format
 */
export interface WebhookEvent {
	/** Event type (e.g., "EntityWasCreated", "EntityWasUpdated", "EntityWasDeleted") */
	event:
		| "EntityWasCreated"
		| "EntityWasUpdated"
		| "EntityWasDeleted"
		| "AppointmentWasCreated"
		| "AppointmentWasUpdated"
		| "AppointmentWasDeleted";
	/** Model type (e.g., "Patient", "Appointment", "Invoice", "Payment") */
	model: "Patient" | "Appointment" | "Invoice" | "Payment" | "Service";
	/** Entity ID */
	id: number;
	/** Entity data payload */
	payload: Record<string, unknown>;
	/** Optional timestamp */
	timestamp?: string;
}

/**
 * Webhook processing context
 */
export interface WebhookContext {
	/** Webhook event data */
	event: WebhookEvent;
	/** Processing timestamp */
	processedAt: Date;
	/** Request ID for tracking */
	requestId: string;
}

/**
 * Context-Aware Sync Types
 *
 * These types support the intelligent sync decision logic that determines
 * the appropriate action to take based on existing database state.
 */

/**
 * Sync action types that determine how to handle platform synchronization
 *
 * - `skip`: Record already synced to target platform, no action needed
 * - `create`: Create new record in target platform
 * - `update`: Update existing record in target platform
 * - `search_then_create`: Search target platform first, create if not found
 */
export type SyncAction = 'skip' | 'create' | 'update' | 'search_then_create';

/**
 * Platform identifiers for sync operations
 *
 * - `cc`: CliniCore (medical practice management platform)
 * - `ap`: AutoPatient (patient engagement and communication platform)
 */
export type SyncPlatform = 'cc' | 'ap';

/**
 * Operation types for sync context
 *
 * - `create`: Creating a new record (from webhook create events)
 * - `update`: Updating an existing record (from webhook update events)
 */
export type SyncOperation = 'create' | 'update';

/**
 * Standard sync operation result interface
 *
 * Provides consistent response format for all sync operations
 * with success/failure status and detailed messaging.
 */
export interface SyncResult {
	/** Whether the sync operation was successful */
	success: boolean;
	/** Human-readable message describing the result */
	message: string;
	/** Optional ID of the created/updated record */
	recordId?: string | number;
	/** Whether the operation was skipped */
	skipped?: boolean;
	/** Any additional data from the sync operation */
	data?: Record<string, unknown>;
}

/**
 * Context information for sync decisions
 *
 * Contains all information needed to understand why a particular
 * sync action was chosen and how it should be executed.
 */
export interface SyncContext {
	/** Determined sync action */
	action: SyncAction;
	/** Target platform for sync */
	targetPlatform: SyncPlatform;
	/** Type of operation being performed */
	operation: SyncOperation;
	/** Reason for the sync decision */
	reason: string;
}

/**
 * Extended sync context for appointment operations
 *
 * Includes additional information about patient sync requirements
 * since appointments depend on patient sync state.
 */
export interface AppointmentSyncContext extends SyncContext {
	/** Specific action for appointment sync */
	appointmentAction: SyncAction;
	/** Whether patient sync is required before appointment sync */
	patientSyncRequired: boolean;
}

// KeyValue type removed as it's not used

export * from "./APTypes";
export * from "./CCTypes";
