import { getDb } from "@database";
import { appointment, patient } from "@database/schema";
import type {
	GetAPAppointmentType,
	GetCCAppointmentType,
	WebhookContext,
} from "@type";
import {
	checkAndAddToBuffer,
	generateAppointmentBuffer<PERSON>ey,
} from "@utils/bufferManager";
import { logSyncError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";
import { apClient } from "../api";
import { determineAppointmentSyncAction } from "../helpers/contextAwareSync";
import { executeAppointmentSync } from "../helpers/syncActions";

/**
 * Processes appointment creation events from CC with automatic patient dependency management
 * Equivalent to ProcessCcAppointmentCreate job from v3Integration
 *
 * **Patient Dependency Management:**
 * - Automatically fetches and syncs patients from CC if they don't exist in local database
 * - Ensures patient exists and is synced to AP before appointment processing
 * - Uses processPatientCreate to handle patient sync with full error handling
 *
 * **Context-Aware Logic:**
 * - Uses intelligent sync decisions to prevent unnecessary API calls
 * - Skips appointment sync if already synced to target platform
 * - Handles patient sync requirements before appointment operations
 *
 * @param payload - Appointment data from CC webhook
 * @param context - Webhook processing context
 * @returns Processing result with context-aware sync status
 */
export async function processAppointmentCreate(
	payload: GetCCAppointmentType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; appointmentId?: string }> {
	try {
		console.log(`Processing appointment create for CC ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generateAppointmentBufferKey(
			"ProcessAppointmentCreate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Appointment creation recently processed, skipping. CC ID: ${payload.id}`,
			};
		}

		const db = getDb();

		// Check if appointment already exists
		const existingAppointment = await db
			.select()
			.from(appointment)
			.where(eq(appointment.ccId, payload.id))
			.limit(1);

		if (existingAppointment.length > 0 && existingAppointment[0].apId) {
			console.log(
				`Appointment already exists in AP, ID: ${existingAppointment[0].apId}`,
			);
			return {
				success: true,
				message: `Appointment already exists in AP, ID: ${existingAppointment[0].apId}`,
				appointmentId: existingAppointment[0].apId,
			};
		}

		// Get the first patient from the appointment
		const patientId =
			payload.patients && payload.patients.length > 0
				? payload.patients[0]
				: null;
		if (!patientId) {
			const message = `No patient associated with appointment CC ID: ${payload.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// CRITICAL FIX: v3Integration behavior - assume patient exists in database
		// v3Integration doesn't fetch patients proactively, it assumes they exist from previous patient events
		const dbPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.ccId, patientId))
			.limit(1)
			.then((results) => results[0]);

		if (!dbPatient) {
			const message = `Patient not found in database for CC Patient ID: ${patientId}. Patient should be synced via patient webhook first.`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// NEW: Use context-aware appointment sync logic
		const appointmentSyncContext = determineAppointmentSyncAction(
			dbPatient,
			existingAppointment.length > 0 ? existingAppointment[0] : null,
			"cc",
			"create",
		);

		console.log(
			`🎯 Appointment sync decision: ${appointmentSyncContext.action} - ${appointmentSyncContext.reason}`,
		);

		// Check if patient sync is required before appointment sync
		if (appointmentSyncContext.patientSyncRequired) {
			const message = `Cannot sync appointment: ${appointmentSyncContext.reason}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Handle skip action (appointment already synced)
		if (appointmentSyncContext.action === "skip") {
			return {
				success: true,
				message: appointmentSyncContext.reason,
				appointmentId: existingAppointment[0]?.apId || undefined,
			};
		}

		// Create or update appointment record in database
		let dbAppointment: typeof appointment.$inferSelect;
		if (existingAppointment.length > 0) {
			const [updatedAppointment] = await db
				.update(appointment)
				.set({
					patientId: dbPatient.id,
					ccData: payload,
					ccUpdatedAt: new Date(payload.updatedAt || new Date().toISOString()),
					updatedAt: new Date(),
				})
				.where(eq(appointment.id, existingAppointment[0].id))
				.returning();
			dbAppointment = updatedAppointment;
		} else {
			const [newAppointment] = await db
				.insert(appointment)
				.values({
					ccId: payload.id,
					patientId: dbPatient.id,
					ccData: payload,
					ccUpdatedAt: new Date(payload.updatedAt || new Date().toISOString()),
				})
				.returning();
			dbAppointment = newAppointment;
		}

		// Execute context-aware appointment sync
		console.log(
			`🔄 Executing context-aware appointment sync for CC ID: ${payload.id}`,
		);
		const syncResult = await executeAppointmentSync(
			dbPatient,
			dbAppointment,
			payload,
			appointmentSyncContext.action,
			"ap",
		);
		console.log(`🔄 Context-aware appointment sync result:`, syncResult);

		// Handle sync result
		if (syncResult.success) {
			// Update database with sync results if we have appointment data
			if (syncResult.data?.apAppointment) {
				await db
					.update(appointment)
					.set({
						apId: syncResult.recordId as string,
						apData: syncResult.data.apAppointment as GetAPAppointmentType,
						apUpdatedAt: new Date(),
						updatedAt: new Date(),
					})
					.where(eq(appointment.id, dbAppointment.id));
			}

			console.log(
				`Appointment sync completed successfully. CC ID: ${payload.id}, Result: ${syncResult.message}`,
			);

			return {
				success: true,
				message: syncResult.message,
				appointmentId: syncResult.recordId?.toString(),
			};
		} else {
			await logSyncError(
				"APPOINTMENT_CREATE_CONTEXT_AWARE_SYNC_FAILED",
				new Error(syncResult.message),
				payload.id,
				"CC",
				"AppointmentProcessor",
			);

			return {
				success: false,
				message: `Failed to sync appointment to AP: ${syncResult.message}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"APPOINTMENT_CREATE_ERROR",
			error,
			payload.id,
			"CC",
			"AppointmentProcessor",
		);

		throw error;
	}
}

/**
 * Processes appointment update events from CC
 * Equivalent to ProcessCcAppointmentUpdate job from v3Integration
 *
 * @param payload - Updated appointment data from CC webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAppointmentUpdate(
	payload: GetCCAppointmentType,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string; appointmentId?: string }> {
	try {
		console.log(`Processing appointment update for CC ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generateAppointmentBufferKey(
			"ProcessAppointmentUpdate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Appointment update recently processed, skipping. CC ID: ${payload.id}`,
			};
		}

		const db = getDb();

		// Find appointment in database
		const dbAppointment = await db
			.select()
			.from(appointment)
			.where(eq(appointment.ccId, payload.id))
			.limit(1)
			.then((results) => results[0]);

		if (!dbAppointment) {
			const message = `Appointment not found in database. CC ID: ${payload.id}`;
			console.log(message);
			await logSyncError(
				"APPOINTMENT_UPDATE_NOT_FOUND",
				new Error(message),
				payload.id,
				"CC",
				"AppointmentProcessor",
			);
			return {
				success: false,
				message,
			};
		}

		// Update appointment in database
		await db
			.update(appointment)
			.set({
				ccData: payload,
				ccUpdatedAt: new Date(payload.updatedAt || new Date().toISOString()),
				updatedAt: new Date(),
			})
			.where(eq(appointment.id, dbAppointment.id));

		// Update appointment in AP if it exists there
		if (dbAppointment.apId) {
			const apUpdateResult = await updateAPAppointment(
				dbAppointment.apId,
				payload,
			);

			if (apUpdateResult.success && apUpdateResult.apAppointment) {
				// Update database with new AP data
				await db
					.update(appointment)
					.set({
						apData: apUpdateResult.apAppointment,
						apUpdatedAt: new Date(),
						updatedAt: new Date(),
					})
					.where(eq(appointment.id, dbAppointment.id));

				console.log(
					`Appointment updated successfully in AP. CC ID: ${payload.id}, AP ID: ${dbAppointment.apId}`,
				);

				return {
					success: true,
					message: `Appointment updated successfully in AP. CC ID: ${payload.id}, AP ID: ${dbAppointment.apId}`,
					appointmentId: dbAppointment.apId,
				};
			} else {
				await logSyncError(
					"APPOINTMENT_UPDATE_AP_FAILED",
					new Error(apUpdateResult.message),
					payload.id,
					"CC",
					"AppointmentProcessor",
				);

				return {
					success: false,
					message: `Failed to update appointment in AP: ${apUpdateResult.message}`,
				};
			}
		} else {
			console.log(
				`Appointment updated in database but not synced to AP. CC ID: ${payload.id}`,
			);
			return {
				success: true,
				message: `Appointment updated in database but not synced to AP. CC ID: ${payload.id}`,
			};
		}
	} catch (error) {
		await logSyncError(
			"APPOINTMENT_UPDATE_ERROR",
			error,
			payload.id,
			"CC",
			"AppointmentProcessor",
		);

		throw error;
	}
}

/**
 * Processes appointment deletion events from CC
 * Equivalent to ProcessCcAppointmentDelete job from v3Integration
 *
 * @param appointmentId - CC appointment ID to delete
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAppointmentDelete(
	appointmentId: number,
	_context: WebhookContext,
): Promise<{ success: boolean; message: string }> {
	try {
		console.log(`Processing appointment delete for CC ID: ${appointmentId}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generateAppointmentBufferKey(
			"ProcessAppointmentDelete",
			appointmentId,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `Appointment deletion recently processed, skipping. CC ID: ${appointmentId}`,
			};
		}

		const db = getDb();

		// Find appointment in database
		const dbAppointment = await db
			.select()
			.from(appointment)
			.where(eq(appointment.ccId, appointmentId))
			.limit(1)
			.then((results) => results[0]);

		if (!dbAppointment) {
			const message = `Appointment not found in database. CC ID: ${appointmentId}`;
			console.log(message);
			return {
				success: true, // Not an error if already deleted
				message,
			};
		}

		// Delete appointment from AP if it exists there
		if (dbAppointment.apId) {
			try {
				await apClient.appointment.delete(dbAppointment.apId);
				console.log(
					`Appointment deleted from AP. AP ID: ${dbAppointment.apId}`,
				);
			} catch (error) {
				console.error(`Failed to delete appointment from AP:`, error);
				await logSyncError(
					"APPOINTMENT_DELETE_AP_FAILED",
					error,
					appointmentId,
					"CC",
					"AppointmentProcessor",
				);
				// Continue with database deletion even if AP deletion fails
			}
		}

		// Delete appointment from database
		await db.delete(appointment).where(eq(appointment.id, dbAppointment.id));

		console.log(`Appointment deleted successfully. CC ID: ${appointmentId}`);

		return {
			success: true,
			message: `Appointment deleted successfully. CC ID: ${appointmentId}`,
		};
	} catch (error) {
		await logSyncError(
			"APPOINTMENT_DELETE_ERROR",
			error,
			appointmentId,
			"CC",
			"AppointmentProcessor",
		);

		throw error;
	}
}

/**
 * Creates an appointment in AP
 *
 * @param apContactId - AP contact ID
 * @param ccAppointment - CC appointment data
 * @returns Result of AP appointment creation
 */
export async function createAPAppointment(
	apContactId: string,
	ccAppointment: GetCCAppointmentType,
): Promise<{
	success: boolean;
	message: string;
	apAppointment?: GetAPAppointmentType;
}> {
	try {
		const appointmentData = {
			contactId: apContactId,
			startTime: ccAppointment.startsAt,
			endTime: ccAppointment.endsAt,
			title: ccAppointment.title || "Appointment",
			appointmentStatus: "confirmed" as const,
		};

		const apAppointment = await apClient.appointment.create(appointmentData);

		return {
			success: true,
			message: "Appointment created successfully in AP",
			apAppointment,
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error(`Failed to create AP appointment:`, error);

		return {
			success: false,
			message: `Failed to create AP appointment: ${errorMessage}`,
		};
	}
}

/**
 * Updates an appointment in AP
 *
 * @param apAppointmentId - AP appointment ID
 * @param ccAppointment - Updated CC appointment data
 * @returns Result of AP appointment update
 */
async function updateAPAppointment(
	apAppointmentId: string,
	ccAppointment: GetCCAppointmentType,
): Promise<{
	success: boolean;
	message: string;
	apAppointment?: GetAPAppointmentType;
}> {
	try {
		const appointmentData = {
			startTime: ccAppointment.startsAt,
			endTime: ccAppointment.endsAt,
			title: ccAppointment.title || "Appointment",
		};

		const apAppointment = await apClient.appointment.update(
			apAppointmentId,
			appointmentData,
		);

		return {
			success: true,
			message: "Appointment updated successfully in AP",
			apAppointment,
		};
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error(`Failed to update AP appointment:`, error);

		return {
			success: false,
			message: `Failed to update AP appointment: ${errorMessage}`,
		};
	}
}
