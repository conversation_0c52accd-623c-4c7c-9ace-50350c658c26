import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { getApiStats } from "./api/optimizedClient";
import { getCacheStats } from "./database/optimizedQueries";
import {
	createApiResponseCache,
	createAppointmentCache,
	createCustomFieldCache,
	createPatientCache,
	getCombinedCacheStats,
} from "./utils/advancedCache";
import {
	clearRequestBuffer,
	getBufferStats,
	initializeRequestBuffer,
} from "./utils/bufferManager";
import {
	cleanupPerformanceData,
	getPerformanceStats,
} from "./utils/performanceMonitor";
import {
	apAppointment<PERSON>and<PERSON>,
	apContactHandler,
} from "./webhook/apHandler";
import { ccHandler } from "./webhook/ccHandler";

const app = new Hono<Env>();
app.use(contextStorage());

// Middleware to initialize request-scoped resources for Cloudflare Workers stateless runtime
app.use("*", async (c, next) => {
	// Initialize request-scoped buffer for duplicate prevention within this request
	initializeRequestBuffer();

	// Create request-scoped cache instances with KV binding
	const kv = c.env?.kv;
	const caches = {
		patient: createPatientCache(kv),
		appointment: createAppointmentCache(kv),
		customField: createCustomFieldCache(kv),
		apiResponse: createApiResponseCache(kv),
	};

	// Store cache instances in context for use by handlers
	c.set("caches", caches);

	try {
		await next();
	} finally {
		// Cleanup request-scoped resources
		clearRequestBuffer();
		// Note: Cache instances are automatically cleaned up when request ends
	}
});

app.onError((err, c) => {
	console.error(err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId: crypto.randomUUID(),
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) => {
	const performanceStats = getPerformanceStats();
	const apiStats = getApiStats();
	const dbCacheStats = getCacheStats();
	const bufferStats = getBufferStats();

	// Get cache instances from context
	const caches = c.get("caches") as
		| {
				patient: import("./utils/advancedCache").CloudflareCache;
				appointment: import("./utils/advancedCache").CloudflareCache;
				customField: import("./utils/advancedCache").CloudflareCache;
				apiResponse: import("./utils/advancedCache").CloudflareCache;
		  }
		| undefined;

	// Get combined cache stats if caches are available
	const advancedCacheStats = caches
		? getCombinedCacheStats([
				caches.patient,
				caches.appointment,
				caches.customField,
				caches.apiResponse,
			])
		: {
				total: {
					hits: 0,
					misses: 0,
					hitRatio: 0,
					memoryEntries: 0,
					kvOperations: 0,
					averageAccessTime: 0,
				},
				individual: [],
			};

	// Cleanup old performance data
	cleanupPerformanceData();

	return c.json({
		status: "healthy",
		timestamp: new Date().toISOString(),
		version: "2.0.0",
		runtime: "cloudflare-workers",
		performance: {
			activeTimers: performanceStats.activeTimers,
			activeRequests: performanceStats.activeRequests,
			memoryUsage: performanceStats.memoryUsage,
			averageRequestTime: performanceStats.averageRequestTime,
			slowRequests: performanceStats.slowRequests,
			criticalRequests: performanceStats.criticalRequests,
			optimizationFlags: performanceStats.optimizationFlags,
		},
		api: {
			circuitBreakers: apiStats.circuitBreakers,
			activeRequests: apiStats.activeRequests,
			cacheSize: apiStats.cacheSize,
		},
		cache: {
			advanced: advancedCacheStats,
			database: {
				size: dbCacheStats.size,
				memoryUsage: dbCacheStats.memoryUsage,
			},
		},
		buffer: bufferStats,
	});
});

// Secure CC webhook endpoint with token verification
// Implements security requirements for CC webhook processing
app.post("/cc/webhook/", async (c) => {
	// Extract authorization header
	const authHeader = c.req.header("Authorization");
	const expectedToken = "dermacare-secure-token-2024";

	// Verify token
	if (!authHeader || !authHeader.startsWith("Bearer ")) {
		return c.json(
			{ error: "unauthorized request", message: "Missing or invalid authorization header" },
			401
		);
	}

	const token = authHeader.substring(7); // Remove "Bearer " prefix
	if (token !== expectedToken) {
		return c.json(
			{ error: "unauthorized request", message: "Invalid token" },
			401
		);
	}

	// Token is valid, proceed with webhook processing
	return ccHandler(c);
});

// AP webhook endpoints for bi-directional sync (AP → CC)
// Consolidated endpoints that auto-detect create/update operations
// Location is now retrieved from config file instead of URL parameter
app.post("/ap/appointment", apAppointmentHandler);
app.post("/ap/contact", apContactHandler);

// Sync services endpoint (from v3Integration - basic implementation)
app.post("/sync-services", (c) => {
	return c.json({
		message: "Sync services endpoint - functionality to be implemented",
		status: "ok",
		timestamp: new Date().toISOString(),
	});
});

export default app;
